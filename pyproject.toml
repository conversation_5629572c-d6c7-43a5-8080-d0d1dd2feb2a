[build-system]
requires = ["setuptools"]
build-backend = "setuptools.build_meta"

[project]
name = "cloudflyer"
version = "1.0.5"
authors = [
    {name = "jackzzs", email = "<EMAIL>"},
]
license = {file="LICENSE"}
description = "A Cloudflare/Turnstile captcha bypass API server."
keywords = [
    "python",
    "recaptcha",
    "solver",
    "cloudflare",
    "bypass",
    "cloudflare-bypass",
    "cloudflare-bypass-script",
    "cloudflare-bypasser",
    "drissionpage",
]
classifiers = [
    "Development Status :: 4 - Beta",
    "Environment :: Console",
    "Intended Audience :: Developers",
    "Operating System :: MacOS",
    "Operating System :: POSIX",
    "Operating System :: Microsoft :: Windows",
    "License :: OSI Approved :: GNU General Public License v3 (GPLv3)",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3 :: Only",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Programming Language :: Python :: 3.13",
    "Programming Language :: Python :: Implementation :: CPython",
]
requires-python = ">=3.10"
dynamic = ["dependencies"]

[project.urls]
Homepage = "https://github.com/zetxtech/cloudflyer"

[project.readme]
file = "README.md"
content-type = "text/markdown"

[project.scripts]
cloudflyer = "cloudflyer.server:main"

[tool.setuptools]
zip-safe = false

[tool.setuptools.dynamic]
dependencies = {file = ["requirements.txt"]}

[tool.setuptools.packages]
find = {namespaces = false}

[tool.black]
line-length = 110

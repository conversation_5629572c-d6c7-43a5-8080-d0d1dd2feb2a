import asyncio
import logging
import socket
from threading import Thread
from typing import Optional

from .handler import Socks5UpstreamHandler

logger = logging.getLogger(__name__)


class ProxyRelayServer:
    def __init__(self, upstream_config: dict):
        self.upstream_handler = Socks5UpstreamHandler(upstream_config)
        self.local_host = '127.0.0.1'
        self.local_port = None
        self.server = None
        self.client_tasks = set()
        self._loop = None
        self._thread = None
        self._shutdown_event = asyncio.Event()

    def start(self, local_host: str = '127.0.0.1'):
        """Starts the proxy server in a background thread."""
        if self._thread and self._thread.is_alive():
            logger.warning("Server is already running.")
            return

        self.local_host = local_host
        self._shutdown_event.clear()
        self._thread = Thread(target=self._run_server_thread, daemon=True)
        self._thread.start()
        logger.info("Proxy server thread started.")

    def stop(self):
        """Stops the proxy server gracefully."""
        if not self._thread or not self._thread.is_alive():
            logger.warning("Server is not running.")
            return

        if self._loop and not self._shutdown_event.is_set():
            logger.info("Initiating server shutdown...")
            self._loop.call_soon_threadsafe(self._shutdown_event.set)
            self._thread.join(timeout=10)
            if self._thread.is_alive():
                logger.error("Server thread failed to stop in time.")
        logger.info("Proxy server stopped.")

    def _run_server_thread(self):
        """The main execution target for the server thread."""
        try:
            self._loop = asyncio.new_event_loop()
            asyncio.set_event_loop(self._loop)
            self._loop.run_until_complete(self._start_and_serve())
        except Exception as e:
            logger.error(f"Error in server thread: {e}", exc_info=True)
        finally:
            self._loop.close()
            logger.info("Event loop closed.")

    async def _start_and_serve(self):
        """Starts the server, serves requests, and handles shutdown."""
        self.local_port = self._find_available_port()
        
        self.server = await asyncio.start_server(
            self._accept_client, self.local_host, self.local_port
        )
        logger.info(f"Proxy server listening on {self.local_host}:{self.local_port}")

        await self._shutdown_event.wait()
        await self._cleanup()

    async def _accept_client(self, client_reader: asyncio.StreamReader,
                             client_writer: asyncio.StreamWriter):
        """Accepts a new client and creates a task to handle it."""
        try:
            initial_data = await client_reader.readexactly(1)
            task = asyncio.create_task(
                self.upstream_handler.handle_request(client_reader, client_writer, initial_data)
            )
            self.client_tasks.add(task)
            task.add_done_callback(self.client_tasks.discard)
        except asyncio.IncompleteReadError:
            logger.warning("Client disconnected before sending data.")
            client_writer.close()

    async def _cleanup(self):
        """Cleans up resources during shutdown."""
        logger.info("Starting cleanup...")
        self.server.close()
        await self.server.wait_closed()
        logger.info("Server has stopped accepting new connections.")

        if self.client_tasks:
            logger.info(f"Cancelling {len(self.client_tasks)} active client tasks...")
            await asyncio.gather(*[task for task in self.client_tasks], return_exceptions=True)
        
        logger.info("Cleanup complete.")

    def _find_available_port(self) -> int:
        """Finds and returns an available TCP port."""
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.bind((self.local_host, 0))
            return s.getsockname()[1]

    def get_proxy_url(self, protocol: str = 'socks5') -> Optional[str]:
        """Returns the proxy URL if the server is running."""
        if not self.local_port:
            logger.warning("Server not running, cannot get proxy URL.")
            return None
        return f"{protocol}://{self.local_host}:{self.local_port}"

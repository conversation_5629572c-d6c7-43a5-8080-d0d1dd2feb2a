# Cloudflyer

Cloudflyer 是一个 Python 服务，帮助解决各种网络安全挑战，包括 Cloudflare 挑战、Turnstile 验证码和 reCAPTCHA Invisible。

## 功能特性

- Cloudflare 挑战解决器
- Turnstile 验证码解决器
- reCAPTCHA Invisible 解决器
- 代理支持 (HTTP/SOCKS)
- 并发任务处理
- RESTful API 服务器

## 安装

```bash
pip install cloudflyer
```

## 快速开始

### 示例脚本

```bash
# 使用代理运行 cloudflare 解决示例
python test.py cloudflare -x socks5://127.0.0.1:1080

# 运行 turnstile 解决示例
python test.py turnstile

# 运行 recaptcha invisible 解决示例
python test.py recaptcha
```

### 解决器服务器

```bash
cloudflyer -K YOUR_CLIENT_KEY
```

选项：
- `-K, --clientKey`: 客户端 API 密钥（必需）
- `-M, --maxTasks`: 最大并发任务数（默认：1）
- `-P, --port`: 服务器监听端口（默认：3000）
- `-H, --host`: 服务器监听主机（默认：localhost）
- `-T, --timeout`: 最大任务超时时间（秒）（默认：120）

### Docker

Docker：

```bash
docker run -it --rm -p 3000:3000 jackzzs/cloudflyer -K YOUR_CLIENT_KEY
```

Docker Compose：

```yaml
version: 3
services:
  cloudflyer:
    image: jackzzs/cloudflyer
    container_name: cloudflyer
    restart: unless-stopped
    ports:
      - 3000:3000
```

## 服务器 API 端点

### 创建任务

请求：

```
POST /createTask
Content-Type: application/json

{
  "clientKey": "your_client_key",
  "type": "CloudflareChallenge",
  "url": "https://example.com",
  "userAgent": "...",
  "proxy": {
    "scheme": "socks5",
    "host": "127.0.0.1",
    "port": 1080
  },
  "content": false
}
```

1. 字段 `userAgent` 和 `proxy` 是可选的。
2. 支持的任务类型：`CloudflareChallenge`、`Turnstile`、`RecaptchaInvisible`
3. 对于 `Turnstile` 任务，需要 `siteKey`。
4. 对于 `RecaptchaInvisible` 任务，需要 `siteKey` 和 `action`。
5. 对于 `CloudflareChallenge` 任务，将 `content` 设置为 true 以在 `response` 中获取页面 HTML。

响应：

```
{
    "taskId": "21dfdca6-fbf5-4313-8ffa-cfc4b8483cc7"
}
```

### 获取任务结果

请求：

```
POST /getTaskResult
Content-Type: application/json
{
  "clientKey": "your_client_key",
  "taskId": "21dfdca6-fbf5-4313-8ffa-cfc4b8483cc7"
}
```

响应：

```
{
    "status": "completed",
    "result": {
        "success": true,
        "code": 200,
        "response": {
            ...
        },
        "data": {
            "type": "CloudflareChallenge",
            ...(input)
        }
    }
}
```

对于 `Turnstile` 任务：

```
"response": {
    "token": "..."
}
```

对于 `CloudflareChallenge` 任务：

```
"response": {
    "cookies": {
        "cf_clearance": "..."
    },
    "headers": {
        "User-Agent": "..."
    },
    "content": "..."
}
```

对于 `RecaptchaInvisible` 任务：

```
"response": {
    "token": "..."
}
```

## WSSocks

[WSSocks](https://github.com/zetxtech/wssocks) 是一个通过 websocket 进行内网穿透的 socks 代理代理。它可以用于连接到用户的网络。

代理端：

```bash
wssocks server -r -t example_token -a -dd
```

用户端（使用代理）：

```bash
wssocks client -u https://ws.zetx.tech -r -t example_token -T 1 -c example_connector_token -dd -E -x socks5://127.0.0.1:1080
```

解决器端：

```
POST /createTask
Content-Type: application/json

{
  "clientKey": "your_client_key",
  "type": "CloudflareChallenge",
  "url": "https://example.com",
  "userAgent": "...",
  "wssocks": {
    "url": "https://ws.zetx.tech",
    "token": "example_connector_token"
  }
}
```

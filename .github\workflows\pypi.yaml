name: PyPi Publish

on:
  workflow_dispatch:
  release:
    types: [released]

permissions:
  contents: read

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      -
        uses: actions/checkout@v4
      -
        name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.10'
      -
        name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install build
      -
        name: Build package
        run: python -m build
      -
        name: Upload dists
        uses: actions/upload-artifact@v4
        with:
          name: "dist"
          path: "dist/"

  publish:
    needs: [build]
    runs-on: ubuntu-latest
    permissions:
      contents: write
      id-token: write
    steps:
      -
        name: Download dists
        uses: actions/download-artifact@v4
        with:
          name: "dist"
          path: "dist/"
      -
        name: Upload to GitHub Release
        env:
          GITHUB_TOKEN: "${{ secrets.GITHUB_TOKEN }}"
        run: |
          gh release upload ${{ github.ref_name }} dist/* --repo ${{ github.repository }}
      -
        name: Publish to <PERSON>y<PERSON>
        uses: pypa/gh-action-pypi-publish@release/v1

# -*- coding: utf-8 -*-
"""
@Project    : my_test
@File       : 批量测试端口.py
@Time       : 2024/9/9 14:19
<AUTHOR> huazz
@Description: Description of 批量测试端口.py
@Version    : 1.0
"""
import requests

url = 'https://www.potatochipssettlement.com/Claim'
# url = 'https://httpbin.org/ip'

proxy = ''
proxies = {'http': proxy, 'https': proxy}
r = requests.get(url, proxies=proxies)
print(r.text)
print(r.status_code)

from bs4 import BeautifulSoup

soup = BeautifulSoup(r.content, 'html.parser')
title = soup.title.string
print(title)
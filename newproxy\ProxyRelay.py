import logging
import time
from typing import Optional
from urllib.parse import urlparse

from proxy import ProxyRelayServer

# 配置日志
logging.basicConfig(
    level=logging.WARNING,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class Socks5Relay:
    def __init__(self, upstream_url: Optional[str] = None):
        self.upstream_config: Optional[dict] = None
        self.server: Optional[ProxyRelayServer] = None

        if upstream_url:
            self.set_upstream_from_url(upstream_url)

    def set_upstream_from_url(self, upstream_url: str):
        """从URL格式设置上游SOCKS5代理"""
        try:
            parsed = urlparse(upstream_url)

            if parsed.scheme.lower() not in ('socks5', 'socks5h'):
                raise ValueError("URL scheme必须是socks5或socks5h")

            host = parsed.hostname
            port = parsed.port or 1080  # 默认端口1080

            username = parsed.username
            password = parsed.password

            self.upstream_config = {
                'host': host,
                'port': port,
                'username': username,
                'password': password
            }
            logger.info(f"上游代理已更新: {host}:{port}")

            if username and password:
                logger.info(f"使用认证: {username}:***")
            else:
                logger.info("使用无认证模式")

        except Exception as e:
            logger.error(f"解析上游URL失败: {e}")
            self.upstream_config = None

    def start(self, local_host: str = '127.0.0.1'):
        """同步启动代理服务器（在后台线程中运行）"""
        if self.server and self.server._thread and self.server._thread.is_alive():
            logger.warning("代理服务器已在运行")
            return

        if not self.upstream_config:
            raise ValueError("未配置上游代理。请先调用 set_upstream_from_url()")

        self.server = ProxyRelayServer(self.upstream_config)
        self.server.start(local_host)

        # 等待服务器启动并分配端口
        start_time = time.time()
        while not self.server.local_port:
            if time.time() - start_time > 10:  # 10秒超时
                logger.error("代理服务器启动超时")
                self.stop()
                return
            time.sleep(0.1)

        logger.info(f"代理服务器已在后台线程启动: {self.get_http_url()}")

    def stop(self):
        """同步停止代理服务器"""
        if self.server:
            self.server.stop()
            self.server = None
        else:
            logger.warning("代理服务器未运行")

    def get_socks5_url(self) -> Optional[str]:
        """获取SOCKS5代理URL"""
        if not self.server or not self.server.local_port:
            logger.warning("代理服务器未启动或未就绪，无法获取代理URL")
            return None
        return self.server.get_proxy_url('socks5')

    def get_http_url(self) -> Optional[str]:
        """获取HTTP代理URL"""
        if not self.server or not self.server.local_port:
            logger.warning("代理服务器未启动或未就绪，无法获取代理URL")
            return None
        return self.server.get_proxy_url('http')

    def get_local_proxy_url(self) -> Optional[str]:
        """获取本地代理URL（兼容性方法，推荐使用get_socks5_url）"""
        return self.get_socks5_url()

    def __enter__(self):
        """支持with语句"""
        self.start()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """支持with语句"""
        self.stop()
        return False  # 不抑制异常


# 示例使用
if __name__ == "__main__":
    # 为了在直接运行时能够找到`proxy`模块，我们可能需要处理路径问题。
    # 在实际项目中，通常会通过包的方式来运行（例如 `python -m payTest.utils.ProxyRelay`）。
    # 如果直接运行此文件出现 `ModuleNotFoundError`, 请确保 `payTest/utils` 目录
    # 位于PYTHONPATH中，或者从项目根目录（`melon/`）运行。
    from DrissionPage import ChromiumPage, ChromiumOptions
    # python test.py cloudflare -x socks5://0465846b31e1f583b17c__cr.us:<EMAIL>:19965
    # 简单使用
    with Socks5Relay("socks5h://0465846b31e1f583b17c__cr.us:<EMAIL>:19965") as relay:
        print(f"SOCKS5代理URL: {relay.get_socks5_url()}")
        print(f"HTTP代理URL: {relay.get_http_url()}")
        print("代理服务器正在运行...按Ctrl+C停止")
        options = ChromiumOptions().auto_port()
        options.set_proxy(relay.get_http_url())
        # options.set_proxy('socks5://127.0.0.1:9000')
        # 创建浏览器实例
        page = ChromiumPage(addr_or_opts=options)
        page.get('https://ip.smartproxy.com/')
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            pass

    # # 或分步使用
    # relay = Socks5Relay()
    # relay.set_upstream_from_url("socks5://user:paw@127.0.0.1:9000")
    # relay.start()
    # print(f"SOCKS5代理URL: {relay.get_socks5_url()}")
    # print(f"HTTP代理URL: {relay.get_http_url()}")
    #
    # # 一段时间后停止
    # time.sleep(3600)
    # relay.stop() 